"""
    pygments.lexers.compiled
    ~~~~~~~~~~~~~~~~~~~~~~~~

    Just export lexer classes previously contained in this module.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

# ruff: noqa: F401
from pygments.lexers.jvm import <PERSON><PERSON>exer, Scala<PERSON>ex<PERSON>
from pygments.lexers.c_cpp import <PERSON><PERSON><PERSON><PERSON>, CppLexer
from pygments.lexers.d import DLexer
from pygments.lexers.objective import ObjectiveCLexer, \
    ObjectiveCppLexer, LogosLexer
from pygments.lexers.go import GoLexer
from pygments.lexers.rust import RustLexer
from pygments.lexers.c_like import <PERSON>Lexer, ValaLexer, CudaLexer
from pygments.lexers.pascal import DelphiLexer, PortugolLexer, Modula2Lexer
from pygments.lexers.ada import AdaLexer
from pygments.lexers.business import <PERSON><PERSON><PERSON><PERSON><PERSON>, CobolFreeformatLexer
from pygments.lexers.fortran import <PERSON>ran<PERSON>exer
from pygments.lexers.prolog import Prolog<PERSON>exer
from pygments.lexers.python import CythonLexer
from pygments.lexers.graphics import GLShaderLexer
from pygments.lexers.ml import OcamlLexer
from pygments.lexers.basic import BlitzBasicLexer, BlitzMaxLexer, MonkeyLexer
from pygments.lexers.dylan import DylanLexer, DylanLidLexer, DylanConsoleLexer
from pygments.lexers.ooc import OocLexer
from pygments.lexers.felix import FelixLexer
from pygments.lexers.nimrod import NimrodLexer
from pygments.lexers.crystal import CrystalLexer

__all__ = []
