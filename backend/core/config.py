import os
import yaml
from pathlib import Path
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from functools import lru_cache


class LogConfig(BaseModel):
    """日志配置"""
    dir: str = Field(description="日志目录")
    name: str = Field(description="日志文件名")
    level: str = Field(default="info", description="日志级别")
    max_size_m: int = Field(default=5, description="日志文件最大大小(MB)")


class CorsConfig(BaseModel):
    """跨域配置"""
    origins: List[str] = Field(default_factory=list, description="允许的源")
    allow_credentials: bool = Field(default=True, description="是否允许凭证")
    allow_methods: List[str] = Field(default_factory=lambda: ["*"], description="允许的方法")
    allow_headers: List[str] = Field(default_factory=lambda: ["*"], description="允许的头部")


class ApiConfig(BaseModel):
    """API配置"""
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8080, description="服务器端口")
    reload: bool = Field(default=False, description="是否启用热重载")
    cors: CorsConfig = Field(default_factory=CorsConfig, description="跨域配置")


class Config(BaseModel):
    """应用配置"""
    model_config = ConfigDict(extra="allow")

    log: LogConfig = Field(description="日志配置")
    api: ApiConfig = Field(description="API配置")


# 全局配置实例
_config: Optional[Config] = None


def load_config(env: str) -> Config:
    """
    加载配置文件

    Args:
        env: 环境名称，默认为'dev'

    Returns:
        Config: 配置对象

    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML解析错误
        ValueError: 配置验证错误
    """

    # 获取配置文件路径
    config_dir = Path(__file__).parent.parent / "config"
    config_file = config_dir / f"config.{env}.yaml"

    # 如果环境特定的配置文件不存在，尝试使用默认配置文件
    if not config_file.exists():
        config_file = config_dir / "config.yaml"

    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    try:
        # 读取YAML文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # 验证并创建配置对象
        return Config(**config_data)

    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"YAML解析错误: {e}")
    except Exception as e:
        raise ValueError(f"配置验证错误: {e}")


@lru_cache()
def get_config(env: str = os.getenv('ENV', 'dev')) -> Config:
    """
    获取配置实例（带缓存）

    Returns:
        Config: 配置对象
    """
    return load_config(env)