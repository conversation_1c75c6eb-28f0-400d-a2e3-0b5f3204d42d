#!/usr/bin/env python3
"""
配置使用示例

这个文件展示了如何在应用中使用配置系统。
"""

import sys
from pathlib import Path

# 添加backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.config import get_config, reload_config


def main():
    """主函数，展示配置使用方法"""
    
    print("=== 配置系统使用示例 ===\n")
    
    # 1. 获取配置
    print("1. 加载配置...")
    config = get_config()
    print("✓ 配置加载成功\n")
    
    # 2. 访问日志配置
    print("2. 日志配置:")
    print(f"   目录: {config.log.dir}")
    print(f"   文件名: {config.log.name}")
    print(f"   级别: {config.log.level}")
    print(f"   最大大小: {config.log.max_size_m}MB\n")
    
    # 3. 访问API配置
    print("3. API配置:")
    print(f"   主机: {config.api.host}")
    print(f"   端口: {config.api.port}")
    print(f"   热重载: {config.api.reload}\n")
    
    # 4. 访问跨域配置
    print("4. 跨域配置:")
    print(f"   允许的源: {config.api.cors.origins}")
    print(f"   允许凭证: {config.api.cors.allow_credentials}")
    print(f"   允许的方法: {config.api.cors.allow_methods}")
    print(f"   允许的头部: {config.api.cors.allow_headers}\n")
    
    # 5. 配置序列化
    print("5. 配置序列化:")
    config_dict = config.model_dump()
    print("   配置已序列化为字典格式")
    print(f"   字典键: {list(config_dict.keys())}\n")
    
    # 6. 配置验证
    print("6. 配置验证:")
    try:
        # 尝试创建一个无效配置
        from core.config import Config, LogConfig, ApiConfig
        
        invalid_config_data = {
            "log": {
                "dir": "/tmp",
                "name": "test.log"
            },
            "api": {
                "host": "127.0.0.1",
                "port": "invalid_port"  # 这应该是整数
            }
        }
        
        Config(**invalid_config_data)
        print("   ❌ 验证失败 - 应该抛出错误")
        
    except Exception as e:
        print(f"   ✓ 验证成功 - 捕获到预期错误: {type(e).__name__}")
    
    print("\n7. 环境特定配置:")
    print("   当前使用的配置文件: config.yaml")
    print("   可以通过设置ENV环境变量来使用不同的配置文件")
    print("   例如: ENV=prod 将加载 config.prod.yaml")
    
    print("\n=== 示例完成 ===")


def demonstrate_config_reload():
    """演示配置重新加载"""
    print("\n=== 配置重新加载示例 ===")
    
    # 获取当前配置
    config1 = get_config()
    print(f"第一次加载 - 端口: {config1.api.port}")
    
    # 重新加载配置
    config2 = reload_config()
    print(f"重新加载后 - 端口: {config2.api.port}")
    
    # 验证是否是不同的实例
    if config1 is config2:
        print("⚠️  配置实例相同 (可能缓存未清理)")
    else:
        print("✓ 配置已重新加载 (不同的实例)")


def demonstrate_config_access_patterns():
    """演示不同的配置访问模式"""
    print("\n=== 配置访问模式示例 ===")
    
    config = get_config()
    
    # 模式1: 直接访问
    print("模式1 - 直接访问:")
    print(f"   日志级别: {config.log.level}")
    
    # 模式2: 使用getattr进行安全访问
    print("\n模式2 - 安全访问:")
    log_level = getattr(config.log, 'level', 'info')
    print(f"   日志级别 (带默认值): {log_level}")
    
    # 模式3: 字典式访问
    print("\n模式3 - 字典式访问:")
    config_dict = config.model_dump()
    log_level = config_dict.get('log', {}).get('level', 'info')
    print(f"   日志级别 (字典访问): {log_level}")
    
    # 模式4: 配置验证
    print("\n模式4 - 配置验证:")
    if hasattr(config.api, 'cors') and config.api.cors.origins:
        print(f"   ✓ 跨域配置已设置，共 {len(config.api.cors.origins)} 个源")
    else:
        print("   ⚠️  跨域配置未设置或为空")


if __name__ == "__main__":
    main()
    demonstrate_config_reload()
    demonstrate_config_access_patterns()
