from pathlib import Path
from schema.file import FileNode
from typing import List



# 工具函数
def get_file_size(file_path: Path) -> int:
    """获取文件大小"""
    try:
        return file_path.stat().st_size
    except:
        return 0

def get_last_modified(file_path: Path) -> str:
    """获取文件最后修改时间"""
    try:
        return file_path.stat().st_mtime.__str__()
    except:
        return ""

# TODO: 根据配置文件中的设置进行文件忽略
def should_ignore_path(path: Path) -> bool:
    """判断是否应该忽略某个路径"""
    ignore_patterns = {
        '.git', '.svn', '.hg',
        'node_modules', '__pycache__', '.pytest_cache',
        'target', 'build', 'dist', '.next',
        '.vscode', '.idea', '.DS_Store',
        '*.pyc', '*.pyo', '*.pyd',
        '.env', '.env.local', 
        '.venv',
    }
    
    name = path.name
    return (
        name.startswith('.') or
        name in ignore_patterns or
        any(name.endswith(pattern.replace('*', '')) for pattern in ignore_patterns if '*' in pattern)
    )

def build_file_tree(dir_path: Path, workspace_id: str = "", max_depth: int = 10, current_depth: int = 0) -> List[FileNode]:
    """构建文件树"""
    if current_depth >= max_depth:
        return []

    nodes = []
    try:
        # 自定义排序：目录在前，文件在后，然后按名称字母顺序排序
        def sort_key(item):
            # 目录返回 (0, 名称)，文件返回 (1, 名称)
            return (0 if item.is_dir() else 1, item.name.lower())

        for item in sorted(dir_path.iterdir(), key=sort_key):
            if should_ignore_path(item):
                continue

            # 计算相对路径
            if workspace_id:
                relative_path = str(item.relative_to(dir_path.parent))
            else:
                relative_path = item.name

            node = FileNode(
                id=relative_path,
                name=item.name,
                type="directory" if item.is_dir() else "file",
                path=str(item),
                relativePath=relative_path,
                isExpanded=False
            )

            if item.is_file():
                node.size = get_file_size(item)
                node.lastModified = get_last_modified(item)
            elif item.is_dir():
                # 递归获取子目录
                node.children = build_file_tree(
                    item, workspace_id, max_depth, current_depth + 1
                )

            nodes.append(node)
    except PermissionError:
        pass

    return nodes
