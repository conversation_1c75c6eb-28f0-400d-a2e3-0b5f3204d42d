import logging
import os
import time
from rich.logging import RichHandler
from logging import Formatter

logger = logging.getLogger('')

logging.basicConfig(
    level=logging.INFO, 
    format='%(message)s', 
    datefmt='%Y-%m-%d %H:%M:%S', 
    handlers=[RichHandler(markup=True, rich_tracebacks=True), 
              logging.FileHandler(os.path.join(log_dir, f"app.log"), encoding="utf-8")],
)

logger = logging.getLogger("rich")