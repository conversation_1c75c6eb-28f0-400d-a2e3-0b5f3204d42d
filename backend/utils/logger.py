import logging
import os
import time
from rich.logging import RichHandler
from logging import Formatter
from config import get_config, Config

# 项目默认日志管理器
logger = logging.getLogger(__name__)

# TODO: 根据配置文件设置日志级别
logger.setLevel()

# 创建自定义格式器
formatter = logging.Formatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 创建文件处理器
# TODO: 按照配置设置日志路径，日志文件最大大小，以及自动切分日志文件
file_handler = logging.FileHandler(
    "",
    encoding="utf-8"
)
file_handler.setFormatter(formatter)
file_handler.setLevel(logging.INFO)

logger.addHandler(file_handler)

# 创建控制台处理器
console_handler = RichHandler(markup=True, rich_tracebacks=True)
console_handler.setFormatter(logging.Formatter('%(message)s'))
console_handler.setLevel(logging.INFO)

# 防止重复日志（不传播到根logger）
logger.propagate = False

# 为了兼容性，也设置根logger的基本配置（但级别设为WARNING以减少第三方库日志）
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[file_handler]
)